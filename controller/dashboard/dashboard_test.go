package dashboard

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// setupTestRouter 设置测试路由
func setupTestRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	r := gin.New()

	// 注册路由
	dashboardGroup := r.Group("/dashboard")
	{
		dashboardGroup.GET("/business", GetBusiness)
		dashboardGroup.GET("/contact", GetContact)
		dashboardGroup.GET("/metric/operation", GetOperationMetric)
		dashboardGroup.GET("/metric/capacity", GetCapacityMetric)
		dashboardGroup.GET("/metric/deployment", GetDeploymentMetric)
		dashboardGroup.GET("/metric/monitor", GetMonitorMetric)
		dashboardGroup.GET("/metric/plan", GetPlanMetric)
	}

	return r
}

// TestGetContact_ParameterValidation 测试接口人信息的参数验证
func TestGetContact_ParameterValidation(t *testing.T) {
	router := setupTestRouter()

	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
		expectedError  bool
	}{
		{
			name:           "有效的查询参数",
			queryParams:    "?team=测试团队&business=测试业务&month=2025-08",
			expectedStatus: http.StatusOK,
			expectedError:  false,
		},
		{
			name:           "缺少team参数",
			queryParams:    "?business=测试业务&month=2025-08",
			expectedStatus: http.StatusOK,
			expectedError:  true,
		},
		{
			name:           "缺少business参数",
			queryParams:    "?team=测试团队&month=2025-08",
			expectedStatus: http.StatusOK,
			expectedError:  true,
		},
		{
			name:           "缺少month参数",
			queryParams:    "?team=测试团队&business=测试业务",
			expectedStatus: http.StatusOK,
			expectedError:  true,
		},
		{
			name:           "缺少所有参数",
			queryParams:    "",
			expectedStatus: http.StatusOK,
			expectedError:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest("GET", "/dashboard/contact"+tt.queryParams, nil)
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)

			if tt.expectedError {
				assert.NotEqual(t, float64(0), response["code"])
			} else {
				// 注意：由于没有真实的数据库连接，这里会返回数据库错误
				// 在实际测试中，应该使用mock数据库或测试数据库
			}
		})
	}
}

// TestMetricEndpoints_ParameterValidation 测试所有指标接口的参数验证
func TestMetricEndpoints_ParameterValidation(t *testing.T) {
	router := setupTestRouter()

	endpoints := []string{
		"/dashboard/metric/operation",
		"/dashboard/metric/capacity",
		"/dashboard/metric/deployment",
		"/dashboard/metric/monitor",
		"/dashboard/metric/plan",
	}

	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
		expectedError  bool
	}{
		{
			name:           "有效的查询参数",
			queryParams:    "?team=测试团队&business=测试业务&month=2025-08",
			expectedStatus: http.StatusOK,
			expectedError:  false,
		},
		{
			name:           "缺少必要参数",
			queryParams:    "?team=测试团队",
			expectedStatus: http.StatusOK,
			expectedError:  true,
		},
	}

	for _, endpoint := range endpoints {
		for _, tt := range tests {
			t.Run(endpoint+"_"+tt.name, func(t *testing.T) {
				req, _ := http.NewRequest("GET", endpoint+tt.queryParams, nil)
				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)

				assert.Equal(t, tt.expectedStatus, w.Code)

				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// 验证响应格式
				assert.Contains(t, response, "code")
				// 在测试环境中，主要验证接口能正确处理请求

				if tt.expectedError {
					assert.NotEqual(t, float64(0), response["code"])
				}
			})
		}
	}
}

// TestSafeParseInt 测试安全整数转换函数
func TestSafeParseInt(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected int
	}{
		{
			name:     "有效的整数字符串",
			input:    "123",
			expected: 123,
		},
		{
			name:     "负整数",
			input:    "-456",
			expected: -456,
		},
		{
			name:     "零",
			input:    "0",
			expected: 0,
		},
		{
			name:     "无效的字符串",
			input:    "abc",
			expected: 0,
		},
		{
			name:     "空字符串",
			input:    "",
			expected: 0,
		},
		{
			name:     "浮点数字符串",
			input:    "123.45",
			expected: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := safeParseInt(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestSafeParseFloat 测试安全浮点数转换函数
func TestSafeParseFloat(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected float64
	}{
		{
			name:     "有效的浮点数字符串",
			input:    "123.45",
			expected: 123.45,
		},
		{
			name:     "整数字符串",
			input:    "123",
			expected: 123.0,
		},
		{
			name:     "负浮点数",
			input:    "-456.78",
			expected: -456.78,
		},
		{
			name:     "零",
			input:    "0",
			expected: 0.0,
		},
		{
			name:     "无效的字符串",
			input:    "abc",
			expected: 0.0,
		},
		{
			name:     "空字符串",
			input:    "",
			expected: 0.0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := safeParseFloat(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestOperationMetric_ResponseStructure 测试操作风险指标的响应结构
func TestOperationMetric_ResponseStructure(t *testing.T) {
	router := setupTestRouter()

	req, _ := http.NewRequest("GET", "/dashboard/metric/operation?team=测试团队&business=测试业务&month=2025-08", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// 验证响应格式
	assert.Contains(t, response, "code")
	// 在测试环境中，由于没有数据库连接，主要验证接口能正确处理请求
	// 实际的数据结构验证应该在集成测试中进行
}

// TestCapacityMetric_ResponseStructure 测试容量指标的响应结构
func TestCapacityMetric_ResponseStructure(t *testing.T) {
	router := setupTestRouter()

	req, _ := http.NewRequest("GET", "/dashboard/metric/capacity?team=测试团队&business=测试业务&month=2025-08", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// 验证响应格式
	assert.Contains(t, response, "code")
	// 在测试环境中，由于没有数据库连接，主要验证接口能正确处理请求
	// 实际的数据结构验证应该在集成测试中进行
}
