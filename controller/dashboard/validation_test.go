package dashboard

import (
	"strconv"
	"testing"
)

// 注意：TestSafeParseInt 和 TestSafeParseFloat 已在 dashboard_test.go 中定义

// TestParameterValidation 测试参数验证逻辑
func TestParameterValidation(t *testing.T) {
	tests := []struct {
		name     string
		team     string
		business string
		month    string
		wantErr  bool
	}{
		{
			name:     "有效的参数",
			team:     "测试团队",
			business: "测试业务",
			month:    "2025-08",
			wantErr:  false,
		},
		{
			name:     "缺少team参数",
			team:     "",
			business: "测试业务",
			month:    "2025-08",
			wantErr:  true,
		},
		{
			name:     "缺少business参数",
			team:     "测试团队",
			business: "",
			month:    "2025-08",
			wantErr:  true,
		},
		{
			name:     "缺少month参数",
			team:     "测试团队",
			business: "测试业务",
			month:    "",
			wantErr:  true,
		},
		{
			name:     "所有参数都为空",
			team:     "",
			business: "",
			month:    "",
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 模拟参数验证逻辑
			hasError := tt.team == "" || tt.business == "" || tt.month == ""

			if hasError != tt.wantErr {
				t.Errorf("参数验证失败，期望错误: %v, 实际错误: %v", tt.wantErr, hasError)
			}
		})
	}
}

// TestContactInfo_Structure 测试ContactInfo结构体
func TestContactInfo_Structure(t *testing.T) {
	contact := ContactInfo{
		Account:   "test_user",
		Name:      "测试用户",
		StartTime: "09:00",
		EndTime:   "18:00",
	}

	if contact.Account != "test_user" {
		t.Errorf("Account字段错误，期望: test_user, 实际: %s", contact.Account)
	}

	if contact.Name != "测试用户" {
		t.Errorf("Name字段错误，期望: 测试用户, 实际: %s", contact.Name)
	}
}

// TestOperationMetric_Structure 测试OperationMetric结构体
func TestOperationMetric_Structure(t *testing.T) {
	metric := OperationMetric{
		Total:       100,
		NotReported: 10,
		NotChecked:  5,
		NotStandard: 3,
	}

	if metric.Total != 100 {
		t.Errorf("Total字段错误，期望: 100, 实际: %d", metric.Total)
	}

	if metric.NotReported != 10 {
		t.Errorf("NotReported字段错误，期望: 10, 实际: %d", metric.NotReported)
	}
}

// TestCapacityMetric_Structure 测试CapacityMetric结构体
func TestCapacityMetric_Structure(t *testing.T) {
	metric := CapacityMetric{
		Coverage:     85.5,
		SuccessRate:  92.3,
		Efficiency:   78.9,
		AverageHours: 2.5,
	}

	if metric.Coverage != 85.5 {
		t.Errorf("Coverage字段错误，期望: 85.5, 实际: %f", metric.Coverage)
	}

	if metric.SuccessRate != 92.3 {
		t.Errorf("SuccessRate字段错误，期望: 92.3, 实际: %f", metric.SuccessRate)
	}
}

// TestRollbackItem_Structure 测试RollbackItem结构体
func TestRollbackItem_Structure(t *testing.T) {
	item := RollbackItem{
		Name:  "test_app.test_product",
		Value: 49.12,
	}

	if item.Name != "test_app.test_product" {
		t.Errorf("Name字段错误，期望: test_app.test_product, 实际: %s", item.Name)
	}

	if item.Value != 49.12 {
		t.Errorf("Value字段错误，期望: 49.12, 实际: %f", item.Value)
	}
}

// TestNotRecalledInfo_Structure 测试NotRecalledInfo结构体
func TestNotRecalledInfo_Structure(t *testing.T) {
	info := NotRecalledInfo{
		P0: 2,
		P1: 5,
	}

	if info.P0 != 2 {
		t.Errorf("P0字段错误，期望: 2, 实际: %d", info.P0)
	}

	if info.P1 != 5 {
		t.Errorf("P1字段错误，期望: 5, 实际: %d", info.P1)
	}
}

// TestEmptyItem_Structure 测试EmptyItem结构体
func TestEmptyItem_Structure(t *testing.T) {
	item := EmptyItem{
		Team:  "信贷团队",
		Value: 43.20,
	}

	if item.Team != "信贷团队" {
		t.Errorf("Team字段错误，期望: 信贷团队, 实际: %s", item.Team)
	}

	if item.Value != 43.20 {
		t.Errorf("Value字段错误，期望: 43.20, 实际: %f", item.Value)
	}
}

// TestStringToIntConversion 测试字符串到整数的转换
func TestStringToIntConversion(t *testing.T) {
	testCases := map[string]int{
		"0":    0,
		"123":  123,
		"-456": -456,
		"abc":  0, // 无效字符串应该返回0
		"":     0, // 空字符串应该返回0
	}

	for input, expected := range testCases {
		result := safeParseInt(input)
		if result != expected {
			t.Errorf("字符串 '%s' 转换为整数失败，期望: %d, 实际: %d", input, expected, result)
		}
	}
}

// TestStringToFloatConversion 测试字符串到浮点数的转换
func TestStringToFloatConversion(t *testing.T) {
	testCases := map[string]float64{
		"0":       0.0,
		"123.45":  123.45,
		"-456.78": -456.78,
		"abc":     0.0, // 无效字符串应该返回0.0
		"":        0.0, // 空字符串应该返回0.0
	}

	for input, expected := range testCases {
		result := safeParseFloat(input)
		if result != expected {
			t.Errorf("字符串 '%s' 转换为浮点数失败，期望: %f, 实际: %f", input, expected, result)
		}
	}
}

// BenchmarkSafeParseInt 性能测试：整数转换
func BenchmarkSafeParseInt(b *testing.B) {
	for i := 0; i < b.N; i++ {
		safeParseInt("12345")
	}
}

// BenchmarkSafeParseFloat 性能测试：浮点数转换
func BenchmarkSafeParseFloat(b *testing.B) {
	for i := 0; i < b.N; i++ {
		safeParseFloat("123.45")
	}
}

// BenchmarkStandardParseInt 性能测试：标准整数转换（对比）
func BenchmarkStandardParseInt(b *testing.B) {
	for i := 0; i < b.N; i++ {
		strconv.Atoi("12345")
	}
}
