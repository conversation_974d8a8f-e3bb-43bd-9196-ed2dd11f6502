package dashboard

import (
	"context"
	"time"

	"github.com/gin-gonic/gin"

	"dt-common/gintool"
	"stabilityDigitalBase/library/ent/contact"
	"stabilityDigitalBase/library/errs"
	"stabilityDigitalBase/library/mysql"
)

const (
	// DefaultTimeout 默认超时时间
	DefaultTimeout = 30 * time.Second
)

// getDBClientWithContext 获取数据库客户端和上下文
func getDBClientWithContext() (*mysql.Client, context.Context, context.CancelFunc, error) {
	client, err := mysql.Database()
	if err != nil {
		return nil, nil, nil, err
	}

	ctx, cancel := context.WithTimeout(context.Background(), DefaultTimeout)
	return client, ctx, cancel, nil
}

// ContactInfo 接口人信息结构
type ContactInfo struct {
	Account   string `json:"account"`
	Name      string `json:"name"`
	StartTime string `json:"startTime"`
	EndTime   string `json:"endTime"`
}

// GetContact 获取接口人信息
func GetContact(c *gin.Context) {
	team := c.Query("team")
	business := c.Query("business")
	month := c.Query("month")

	if team == "" || business == "" || month == "" {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("team、business、month参数不能为空"))
		return
	}

	// 获取数据库客户端和上下文
	client, ctx, cancel, err := getDBClientWithContext()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	defer cancel()

	// 查询接口人信息
	contacts, err := client.Contact.Query().
		Where(
			contact.TeamEQ(team),
			contact.BusinessEQ(business),
		).
		All(ctx)

	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 构建接口人信息列表
	var result []ContactInfo
	for _, c := range contacts {
		result = append(result, ContactInfo{
			Account:   c.Account,
			Name:      c.Name,
			// StartTime: c.StartTime,
			// EndTime:   c.EndTime,
		})
	}

	gintool.JSON2FE(c, result, nil)
}
