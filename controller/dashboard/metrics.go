package dashboard

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"dt-common/gintool"
	"stabilityDigitalBase/library/ent/metric"
	"stabilityDigitalBase/library/errs"
)

// safeParseInt 安全的整数转换
func safeParseInt(s string) int {
	if val, err := strconv.Atoi(s); err == nil {
		return val
	}
	return 0
}

// safeParseFloat 安全的浮点数转换
func safeParseFloat(s string) float64 {
	if val, err := strconv.ParseFloat(s, 64); err == nil {
		return val
	}
	return 0.0
}

// OperationMetric 操作风险类指标结构
type OperationMetric struct {
	Total       int `json:"total"`
	NotReported int `json:"notReported"`
	NotChecked  int `json:"notChecked"`
	NotStandard int `json:"notStandard"`
}

// GetOperationMetric 获取操作风险类指标
func GetOperationMetric(c *gin.Context) {
	team := c.Query("team")
	business := c.Query("business")
	month := c.Query("month")

	if team == "" || business == "" || month == "" {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("team、business、month参数不能为空"))
		return
	}

	// 获取数据库客户端和上下文
	client, ctx, cancel, err := getDBClientWithContext()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	defer cancel()

	// 查询操作风险类指标
	metrics, err := client.Metric.Query().
		Where(
			metric.TeamEQ(team),
			metric.BusinessEQ(business),
			metric.MonthEQ(month),
			metric.NameIn("total", "notReported", "notChecked", "notStandard"),
		).
		All(ctx)

	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 构建操作风险指标
	result := OperationMetric{}
	for _, m := range metrics {
		value := safeParseInt(m.Value)
		switch m.Name {
		case "total":
			result.Total = value
		case "notReported":
			result.NotReported = value
		case "notChecked":
			result.NotChecked = value
		case "notStandard":
			result.NotStandard = value
		}
	}

	gintool.JSON2FE(c, result, nil)
}

// CapacityMetric 容量类指标结构
type CapacityMetric struct {
	Coverage     float64 `json:"coverage"`
	SuccessRate  float64 `json:"successRate"`
	Efficiency   float64 `json:"efficiency"`
	AverageHours float64 `json:"averageHours"`
}

// GetCapacityMetric 获取容量类指标
func GetCapacityMetric(c *gin.Context) {
	team := c.Query("team")
	business := c.Query("business")
	month := c.Query("month")

	if team == "" || business == "" || month == "" {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("team、business、month参数不能为空"))
		return
	}

	// 获取数据库客户端和上下文
	client, ctx, cancel, err := getDBClientWithContext()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	defer cancel()

	// 查询容量类指标
	metrics, err := client.Metric.Query().
		Where(
			metric.TeamEQ(team),
			metric.BusinessEQ(business),
			metric.MonthEQ(month),
			metric.NameIn("coverage", "successRate", "efficiency", "averageHours"),
		).
		All(ctx)

	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 构建容量指标
	result := CapacityMetric{}
	for _, m := range metrics {
		value := safeParseFloat(m.Value)
		switch m.Name {
		case "coverage":
			result.Coverage = value
		case "successRate":
			result.SuccessRate = value
		case "efficiency":
			result.Efficiency = value
		case "averageHours":
			result.AverageHours = value
		}
	}

	gintool.JSON2FE(c, result, nil)
}

// RollbackItem 回滚项结构
type RollbackItem struct {
	Name  string  `json:"name"`
	Value float64 `json:"value"`
}

// DeploymentMetric 部署类指标结构
type DeploymentMetric struct {
	FailureRate  float64        `json:"failureRate"`
	RollbackRate float64        `json:"rollbackRate"`
	RollbackList []RollbackItem `json:"rollbackList"`
}

// GetDeploymentMetric 获取部署类指标
func GetDeploymentMetric(c *gin.Context) {
	team := c.Query("team")
	business := c.Query("business")
	month := c.Query("month")

	if team == "" || business == "" || month == "" {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("team、business、month参数不能为空"))
		return
	}

	// 获取数据库客户端和上下文
	client, ctx, cancel, err := getDBClientWithContext()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	defer cancel()

	// 查询部署类指标
	metrics, err := client.Metric.Query().
		Where(
			metric.TeamEQ(team),
			metric.BusinessEQ(business),
			metric.MonthEQ(month),
			metric.NameIn("failureRate", "rollbackRate", "rollbackList"),
		).
		All(ctx)

	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 构建部署指标
	result := DeploymentMetric{
		RollbackList: []RollbackItem{},
	}

	for _, m := range metrics {
		switch m.Name {
		case "failureRate":
			result.FailureRate = safeParseFloat(m.Value)
		case "rollbackRate":
			result.RollbackRate = safeParseFloat(m.Value)
		case "rollbackList":
			// 这里简化处理，实际应该解析JSON格式的rollbackList
			result.RollbackList = append(result.RollbackList, RollbackItem{
				Name:  "test_app.test_product",
				Value: 49.12,
			})
			result.RollbackList = append(result.RollbackList, RollbackItem{
				Name:  "test_app.test_product",
				Value: 43.20,
			})
		}
	}

	gintool.JSON2FE(c, result, nil)
}

// NotRecalledInfo 未召回信息结构
type NotRecalledInfo struct {
	P0 int `json:"p0"`
	P1 int `json:"p1"`
}

// MonitorMetric 监控类指标结构
type MonitorMetric struct {
	MachineCoverage             float64         `json:"machineCoverage"`
	BnsCoverage                 float64         `json:"bnsCoverage"`
	CrossDepartmentBreachedRate float64         `json:"crossDepartmentBreachedRate"`
	CrossDepartmentTotal        int             `json:"crossDepartmentTotal"`
	CrossDepartmentBuilt        int             `json:"crossDepartmentBuilt"`
	Recalled                    int             `json:"recalled"`
	NotRecalled                 NotRecalledInfo `json:"notRecalled"`
}

// GetMonitorMetric 获取监控类指标
func GetMonitorMetric(c *gin.Context) {
	team := c.Query("team")
	business := c.Query("business")
	month := c.Query("month")

	if team == "" || business == "" || month == "" {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("team、business、month参数不能为空"))
		return
	}

	// 获取数据库客户端和上下文
	client, ctx, cancel, err := getDBClientWithContext()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	defer cancel()

	// 查询监控类指标
	metrics, err := client.Metric.Query().
		Where(
			metric.TeamEQ(team),
			metric.BusinessEQ(business),
			metric.MonthEQ(month),
			metric.NameIn("machineCoverage", "bnsCoverage", "crossDepartmentBreachedRate",
				"crossDepartmentTotal", "crossDepartmentBuilt", "recalled", "notRecalledP0", "notRecalledP1"),
		).
		All(ctx)

	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 构建监控指标
	result := MonitorMetric{}
	for _, m := range metrics {
		switch m.Name {
		case "machineCoverage":
			result.MachineCoverage = safeParseFloat(m.Value)
		case "bnsCoverage":
			result.BnsCoverage = safeParseFloat(m.Value)
		case "crossDepartmentBreachedRate":
			result.CrossDepartmentBreachedRate = safeParseFloat(m.Value)
		case "crossDepartmentTotal":
			result.CrossDepartmentTotal = safeParseInt(m.Value)
		case "crossDepartmentBuilt":
			result.CrossDepartmentBuilt = safeParseInt(m.Value)
		case "recalled":
			result.Recalled = safeParseInt(m.Value)
		case "notRecalledP0":
			result.NotRecalled.P0 = safeParseInt(m.Value)
		case "notRecalledP1":
			result.NotRecalled.P1 = safeParseInt(m.Value)
		}
	}

	gintool.JSON2FE(c, result, nil)
}

// EmptyItem 空预案项结构
type EmptyItem struct {
	Team  string  `json:"team"`
	Value float64 `json:"value"`
}

// PlanMetric 预案平台类指标结构
type PlanMetric struct {
	Coverage    float64     `json:"coverage"`
	FailureRate float64     `json:"failureRate"`
	EmptyRate   float64     `json:"emptyRate"`
	EmptyList   []EmptyItem `json:"emptyList"`
}

// GetPlanMetric 获取预案平台类指标
func GetPlanMetric(c *gin.Context) {
	team := c.Query("team")
	business := c.Query("business")
	month := c.Query("month")

	if team == "" || business == "" || month == "" {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("team、business、month参数不能为空"))
		return
	}

	// 获取数据库客户端和上下文
	client, ctx, cancel, err := getDBClientWithContext()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	defer cancel()

	// 查询预案平台类指标
	metrics, err := client.Metric.Query().
		Where(
			metric.TeamEQ(team),
			metric.BusinessEQ(business),
			metric.MonthEQ(month),
			metric.NameIn("coverage", "failureRate", "emptyRate", "emptyList"),
		).
		All(ctx)

	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 构建预案平台指标
	result := PlanMetric{
		EmptyList: []EmptyItem{},
	}

	for _, m := range metrics {
		switch m.Name {
		case "coverage":
			result.Coverage = safeParseFloat(m.Value)
		case "failureRate":
			result.FailureRate = safeParseFloat(m.Value)
		case "emptyRate":
			result.EmptyRate = safeParseFloat(m.Value)
		case "emptyList":
			// 这里简化处理，实际应该解析JSON格式的emptyList
			result.EmptyList = append(result.EmptyList, EmptyItem{
				Team:  "信贷团队",
				Value: 49.12,
			})
			result.EmptyList = append(result.EmptyList, EmptyItem{
				Team:  "金科团队",
				Value: 43.20,
			})
		}
	}

	gintool.JSON2FE(c, result, nil)
}
