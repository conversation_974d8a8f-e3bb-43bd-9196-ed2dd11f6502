package router

import (
	"github.com/gin-gonic/gin"

	"stabilityDigitalBase/controller/dashboard"
	"stabilityDigitalBase/controller/incident"
)

// Routes 数字化项目接口
func Routes(r *gin.Engine, token string) {

	// 项目管理路由
	apiGroup := r.Group("/api")
	{
		apiGroup.GET("/ping", func(c *gin.Context) {
			c.String(200, "pong")
		})
	}

	// 故障管理路由
	incidentGroup := r.Group("/incident")
	{
		incidentGroup.POST("/create", incident.Create)             // 故障创建
		incidentGroup.POST("/modify", incident.Modify)             // 故障信息维护
		incidentGroup.POST("/updateStatus", incident.UpdateStatus) // 更新故障状态
		incidentGroup.POST("/uploadFile", incident.UploadFile)     // 附件上传
		incidentGroup.GET("/list", incident.List)                  // 故障列表查询
		incidentGroup.GET("/detail/:id", incident.Detail)          // 故障详情查询

		// 问题管理路由
		incidentGroup.POST("/problem/create", incident.CreateProblem)       // 创建问题
		incidentGroup.POST("/problem/modify", incident.ModifyProblem)       // 修改问题
		incidentGroup.DELETE("/problem/:id", incident.DeleteProblem)        // 删除问题
		incidentGroup.GET("/problems", incident.ListProblems)               // 查询故障的问题列表

		// 改进项管理路由
		incidentGroup.POST("/improvement/create", incident.CreateImprovement)           // 创建改进项
		incidentGroup.POST("/improvement/modify", incident.ModifyImprovement)           // 修改改进项
		incidentGroup.POST("/improvement/updateStatus", incident.UpdateImprovementStatus) // 更新改进项状态
		incidentGroup.DELETE("/improvement/:id", incident.DeleteImprovement)            // 删除改进项
		incidentGroup.GET("/improvements", incident.ListImprovements)                   // 查询问题的改进项列表
	}

	// 仪表盘路由
	dashboardGroup := r.Group("/dashboard")
	{
		dashboardGroup.GET("/business", dashboard.GetBusiness)                  // 业务团队下拉列表
		dashboardGroup.GET("/contact", dashboard.GetContact)                    // 获取接口人信息
		dashboardGroup.GET("/metric/operation", dashboard.GetOperationMetric)   // 操作风险类指标
		dashboardGroup.GET("/metric/capacity", dashboard.GetCapacityMetric)     // 容量类指标
		dashboardGroup.GET("/metric/deployment", dashboard.GetDeploymentMetric) // 部署类指标
		dashboardGroup.GET("/metric/monitor", dashboard.GetMonitorMetric)       // 监控类指标
		dashboardGroup.GET("/metric/plan", dashboard.GetPlanMetric)             // 预案平台类指标
	}
}
