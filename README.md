# 稳定性数字化平台 (Stability Digital Base)

一个基于Go语言开发的故障管理和运维指标监控系统，采用Gin框架和Ent ORM，完全遵循Go语言惯用法进行开发。

## 🎯 项目特色

- **遵循Go惯用法**: 使用package级别函数替代结构体方法，避免不必要的面向对象抽象
- **功能模块化**: 按业务功能合理拆分文件，提高代码可维护性
- **类型安全**: 使用Ent ORM进行类型安全的数据库操作
- **统一响应**: 标准化的API响应格式和错误处理机制
- **完整测试**: 包含单元测试和API测试，确保代码质量

## 🏗️ 技术栈

- **后端框架**: [Gin](https://github.com/gin-gonic/gin) - 高性能的Go Web框架
- **ORM**: [Ent](https://entgo.io/) - Facebook开源的Go实体框架
- **数据库**: MySQL
- **测试框架**: [Testify](https://github.com/stretchr/testify)
- **构建工具**: Make

## 📁 项目结构

```
stabilityDigitalBase/
├── cmd/app/                    # 应用入口
│   └── main.go
├── controller/                 # 控制器层 (Go惯用法)
│   ├── incident/              # 故障管理模块
│   │   ├── incident.go        # 核心故障管理功能
│   │   ├── upload.go          # 附件上传功能
│   │   └── incident_test.go   # 单元测试
│   └── dashboard/             # 仪表盘模块
│       ├── business.go        # 业务团队管理
│       ├── contact.go         # 接口人管理
│       ├── metrics.go         # 指标数据管理
│       └── dashboard_test.go  # 单元测试
├── library/                   # 核心库
│   ├── ent/                   # 数据模型和ORM
│   ├── mysql/                 # 数据库连接
│   └── errs/                  # 错误处理
├── router/                    # 路由配置
│   └── api.go
├── doc/                       # 项目文档
├── test/                      # 测试脚本
├── Makefile                   # 构建脚本
└── README.md
```

## 🚀 快速开始

### 环境要求

- Go 1.21+
- MySQL 8.0+

### 安装依赖

```bash
# 安装Go依赖
make deps
```

### 编译运行

```bash
# 方式1: 使用启动脚本（推荐）
./start.sh

# 方式2: 使用Makefile
make build && make run

# 方式3: 开发模式直接运行
make dev
```

### 状态检查

```bash
# 检查服务运行状态和接口健康度
./check.sh
```

### 运行测试

```bash
# 运行所有测试
make test

# 生成测试覆盖率报告
make test-coverage

# 运行代码质量检查
make quality
```

## 📋 功能模块

### 故障管理模块 (6个接口)

- **故障创建** - `POST /incident/create` - 支持一句话快速创建故障
- **故障修改** - `POST /incident/modify` - 支持修改故障的所有字段
- **状态更新** - `POST /incident/updateStatus` - 独立的状态管理接口
- **附件上传** - `POST /incident/uploadFile` - 支持文件上传和元数据管理
- **故障列表** - `GET /incident/list` - 支持分页查询和多条件筛选
- **故障详情** - `GET /incident/detail/:id` - 完整的故障信息展示

### 仪表盘模块 (7个接口)

- **业务团队** - `GET /dashboard/business` - 业务团队下拉列表
- **接口人信息** - `GET /dashboard/contact` - 根据团队和业务查询接口人
- **操作风险指标** - `GET /dashboard/metric/operation` - 总数、未上报、未检查、不规范统计
- **容量指标** - `GET /dashboard/metric/capacity` - 覆盖率、成功率、效率、平均时长
- **部署指标** - `GET /dashboard/metric/deployment` - 失败率、回滚率、回滚详情
- **监控指标** - `GET /dashboard/metric/monitor` - 机器覆盖率、BNS覆盖率、跨部门指标
- **预案平台指标** - `GET /dashboard/metric/plan` - 覆盖率、失败率、空预案统计

## 🔧 Makefile命令

```bash
make help          # 显示所有可用命令
make all           # 运行完整的构建流程
make clean         # 清理构建文件
make fmt           # 格式化Go代码
make test          # 运行单元测试
make build         # 构建应用程序
make run           # 构建并运行应用程序
make dev           # 开发模式直接运行
make api-test      # 运行API测试脚本
make quality       # 运行代码质量检查
```

## 📊 API响应格式

所有API接口返回统一格式：

```json
{
  "code": 0,
  "message": "success", 
  "data": {...}
}
```

## 🏗️ 架构特点

### Go惯用法实践

项目完全遵循Go语言惯用法，避免将其他语言的编程风格强加到Go中：

**重构前（Java/C#风格）**：
```go
type IncidentController struct{}
func NewIncidentController() *IncidentController { ... }
func (ic *IncidentController) Create(c *gin.Context) { ... }
```

**重构后（Go惯用法）**：
```go
func Create(c *gin.Context) { ... }
```

### 代码组织原则

- **按功能拆分**: 将相关功能组织在同一个package中
- **公共函数提取**: 避免代码重复，提高复用性
- **清晰的命名空间**: 通过package自然分离不同业务逻辑
- **统一的错误处理**: 使用项目统一的错误码机制

## 📚 文档

- [重构总结](doc/重构总结.md) - 详细的重构过程和技术决策
- [项目状态报告](doc/项目状态报告.md) - 完整的项目状态和功能清单

## 🧪 测试

项目包含完整的测试套件：

- **单元测试**: 覆盖所有核心功能的参数验证和业务逻辑
- **API测试**: 提供完整的API测试脚本
- **测试覆盖率**: 自动生成测试覆盖率报告

运行测试：
```bash
# 运行所有测试
make test

# 查看测试覆盖率
make test-coverage
open coverage.html
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证

---

**注**: 本项目是Go语言惯用法的最佳实践示例，展示了如何避免将其他语言的编程风格强加到Go语言中，而是采用Go官方推崇的简洁、直接、实用的开发方式。
