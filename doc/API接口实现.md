# API接口实现任务

## 任务概述
根据doc/api.md文档实现故障管理和仪表盘相关的API接口。

## 实现内容

### 1. 故障管理接口
- ✅ `POST /incident/create` - 一句话故障创建
- ✅ `POST /incident/modify` - 故障信息维护  
- ✅ `POST /incident/updateStatus` - 更新故障状态
- ✅ `POST /incident/uploadFile` - 附件上传
- ✅ `GET /incident/list` - 故障列表查询
- ✅ `GET /incident/detail` - 故障详情查询

### 2. 仪表盘接口
- ✅ `GET /dashboard/business` - 业务团队下拉列表
- ✅ `GET /dashboard/contact` - 获取接口人信息
- ✅ `GET /dashboard/metric/operation` - 操作风险类指标
- ✅ `GET /dashboard/metric/capacity` - 容量类指标
- ✅ `GET /dashboard/metric/deployment` - 部署类指标
- ✅ `GET /dashboard/metric/monitor` - 监控类指标
- ✅ `GET /dashboard/metric/plan` - 预案平台类指标

## 技术实现

### 文件结构
```
controller/
├── incident/
│   └── incident.go          # 故障管理控制器
└── dashboard/
    ├── dashboard.go          # 仪表盘控制器
    └── user.go              # 原有文件
router/
└── api.go                   # 路由配置
```

### 关键技术点
1. **响应格式**: 使用`gintool.JSON2FE`确保返回`{code: 0, message: "success", data: any}`格式
2. **数据库操作**: 使用`mysql.Database()`获取ent客户端
3. **错误处理**: 使用项目统一的`errs.Code`错误处理机制
4. **参数验证**: 使用gin的`ShouldBindJSON`和`ShouldBindQuery`进行参数绑定和验证

### 数据模型
- 复用现有的ent schema定义
- Incident: 故障主表
- Timeline: 时间线表
- Improvement: 改进项表
- Attachment: 附件表
- Contact: 接口人表
- Metric: 指标数据表

## 测试建议
1. 使用Postman或curl测试所有API接口
2. 验证参数验证逻辑
3. 测试错误处理机制
4. 验证响应格式符合API文档要求

## 注意事项
1. 附件上传接口中的FTP上传功能需要后续完善
2. 部分复杂数据结构（如rollbackList、emptyList）目前使用模拟数据
3. 时间格式统一使用"2006-01-02"和"2006-01-02 15:04:05"
4. 所有接口都使用统一的错误处理和响应格式
