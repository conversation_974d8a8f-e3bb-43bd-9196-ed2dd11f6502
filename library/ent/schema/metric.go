package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

// Metric holds the schema definition for the Metric entity.
type Metric struct {
	ent.Schema
}

// Fields of the Metric.
func (Metric) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id").Unique().Comment("主键ID"),

		field.String("team").MaxLen(50).NotEmpty().Comment("团队"),
		field.String("business").MaxLen(50).NotEmpty().Comment("业务"),

		field.String("name").MaxLen(100).NotEmpty().Comment("指标名"),
		field.String("alias").MaxLen(100).NotEmpty().Comment("别名"),
		field.Text("value").NotEmpty().Comment("值"),
		field.String("month").MaxLen(10).NotEmpty().Comment("统计月份 2025-07"),

		field.Time("created_at").StructTag(`json:"createdAt"`).Default(time.Now).Comment("创建时间"),
		field.Time("updated_at").StructTag(`json:"updatedAt"`).Default(time.Now).UpdateDefault(time.Now).Comment("更新时间"),
	}
}

// Edges of the Metric.
func (Metric) Edges() []ent.Edge {
	return []ent.Edge{}
}

// Indexes of the Metric.
func (Metric) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("team", "business", "name", "month").Unique(),
	}
}

// Annotations of the Metric.
func (Metric) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "metric"},
	}
}
